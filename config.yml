tool:
  module: tools.testssl_tool
  class: TestSSLTool
  options: {}

input:
  # Choose ONE of the input modes below

  # 1) Load all JSON files from a directory
  type: dir
  path: ./inputs
  pattern: "*.json"

  # 2) Or specify explicit files
  # type: files
  # paths:
  #   - ./inputs/a.json
  #   - ./inputs/b.json

  # 3) Or use a glob from the current working directory
  # type: glob
  # pattern: "inputs/*.json"

# Jinja2 template path
template: templates/ssl_report.md.j2

# Output Markdown file
output: report.md
