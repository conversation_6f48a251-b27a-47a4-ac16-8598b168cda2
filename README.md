# testssl_reporter

Reusable engine + tool + template to render Markdown reports from `testssl.sh` JSON.

## Layout
- `engine.py` — generic Jinja2 rendering engine (config-driven)
- `tools/testssl_tool.py` — parses testssl JSON files, produces template context
- `templates/ssl_report.md.j2` — Jinja2 Markdown template
- `config.yml` — example configuration
- `inputs/` — put your `*.json` files here
- `requirements.txt` — Python dependencies

## Usage

```bash
python -m venv .venv
. .venv/bin/activate  # Windows: .venv\Scripts\activate
pip install -r requirements.txt

# Put testssl JSONs into ./inputs (or edit config.yml)
python engine.py -c config.yml
# Output -> report.md
```

### Overrides

```bash
# Choose a different template or output path
python engine.py -c config.yml --template templates/ssl_report.md.j2 --out my-report.md
```

### Config

See `config.yml` for supported `input` modes: `dir`, `files`, `glob`.
