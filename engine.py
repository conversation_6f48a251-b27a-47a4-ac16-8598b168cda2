#!/usr/bin/env python3
"""
Reusable rendering engine.
- Loads a config (YAML or JSON) describing the tool, inputs, template, and output.
- Dynamically loads a Tool class (must provide process(inputs, options) -> context dict).
- Renders the specified Jinja2 Markdown template using the returned context.

CLI:
  python engine.py -c config.yml
  # Optional overrides:
  python engine.py -c config.yml --template templates/ssl_report.md.j2 --out report.md
"""
from __future__ import annotations
import argparse
import json
import importlib
from pathlib import Path
from typing import Dict, Any, List

try:
    import yaml  # type: ignore
    _HAS_YAML = True
except Exception:
    _HAS_YAML = False

from jinja2 import Environment, FileSystemLoader


def _load_config(path: Path) -> Dict[str, Any]:
    text = path.read_text(encoding="utf-8")
    if path.suffix.lower() in {".yml", ".yaml"}:
        if not _HAS_YAML:
            raise RuntimeError("PyYAML is required to load YAML configs. Install with: pip install pyyaml")
        return yaml.safe_load(text)  # type: ignore
    # fallback: try JSON
    return json.loads(text)


def _resolve_inputs(cfg_input: Dict[str, Any]) -> List[Path]:
    t = (cfg_input.get("type") or "").lower()
    files: List[Path] = []
    if t == "dir":
        base = Path(cfg_input["path"]).expanduser()
        pattern = cfg_input.get("pattern", "*.json")
        if not base.is_dir():
            raise NotADirectoryError(f"Input directory not found: {base}")
        files.extend(sorted(base.glob(pattern)))
    elif t == "files":
        for p in cfg_input.get("paths", []):
            files.append(Path(p).expanduser())
    elif t == "glob":
        pattern = cfg_input.get("pattern")
        if not pattern:
            raise ValueError("input.type=glob requires 'pattern'")
        files.extend(Path().glob(pattern))
    else:
        raise ValueError(f"Unsupported input.type: {t}")

    # de-duplicate while preserving order
    seen = set(); unique: List[Path] = []
    for p in files:
        rp = str(p.resolve())
        if rp not in seen:
            seen.add(rp); unique.append(p)
    return unique


def _load_tool(module_path: str, class_name: str):
    mod = importlib.import_module(module_path)
    cls = getattr(mod, class_name)
    return cls()


def main():
    ap = argparse.ArgumentParser()
    ap.add_argument("-c", "--config", required=True, help="Path to YAML/JSON config")
    ap.add_argument("--template", help="Override template path")
    ap.add_argument("--out", help="Override output path")
    args = ap.parse_args()

    cfg = _load_config(Path(args.config))

    tool_cfg = cfg.get("tool", {})
    input_cfg = cfg.get("input", {})
    template_path = Path(args.template or cfg.get("template"))
    output_path = Path(args.out or cfg.get("output"))

    if not template_path:
        raise ValueError("Missing template path (template)")
    if not output_path:
        raise ValueError("Missing output path (output)")

    inputs = _resolve_inputs(input_cfg)

    tool = _load_tool(tool_cfg.get("module"), tool_cfg.get("class"))
    options = tool_cfg.get("options", {})

    # Let the tool process inputs into a context dict (e.g., {"scope":..., "endpoints":...})
    context: Dict[str, Any] = tool.process(inputs, options)

    # Render template
    env = Environment(loader=FileSystemLoader(str(template_path.parent)), autoescape=False, trim_blocks=True, lstrip_blocks=True)
    template = env.get_template(template_path.name)
    rendered = template.render(**context)

    output_path.write_text(rendered, encoding="utf-8")
    print(f"Rendered → {output_path}")


if __name__ == "__main__":
    main()
