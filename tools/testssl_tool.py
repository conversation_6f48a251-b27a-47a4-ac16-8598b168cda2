"""
Tool implementation for testssl.sh JSON outputs.
Provides: TestSSLTool.process(inputs, options) -> context dict
Context keys used by the template: scope (with flags), endpoints (list)
"""
from __future__ import annotations
import json
from pathlib import Path
from typing import List, Dict, Any
from collections import defaultdict


class TestSSLTool:
    """Parse one or more testssl JSON files into template context."""

    def _calculate_cell_value(self, value: Any, *, offered_text="Enabled", not_offered_text="Disabled", not_present_text=None) -> str:
        s = str(value or "").lower()
        if not s and not_present_text is not None:
            return not_present_text
        if "not offered" in s:
            return not_offered_text
        if "offered" in s:
            return offered_text
        return not_present_text if not_present_text is not None else not_offered_text

    def _build_context(self, records: List[Dict[str, Any]]) -> Dict[str, Any]:
        # group by endpoint (display, port)
        grouped: Dict[tuple, List[Dict[str, Any]]] = defaultdict(list)
        for rec in records:
            ip = rec.get("ip", "")
            port = str(rec.get("port", ""))
            display = ip.split("/", 1)[1] if "/" in ip and not ip.startswith("/") else ip
            grouped[(display or "unknown", port)].append(rec)

        endpoints: List[Dict[str, Any]] = []

        for (display, port), recs in grouped.items():
            idx = { (r.get("id") or "").strip(): (r.get("finding") or "").strip() for r in recs }

            tls13_val = idx.get("TLS1_3", "")
            protocols = {
                "SSLv2":  self._calculate_cell_value(idx.get("SSLv2")),
                "SSLv3":  self._calculate_cell_value(idx.get("SSLv3")),
                "TLS1_0": self._calculate_cell_value(idx.get("TLS1")),
                "TLS1_1": self._calculate_cell_value(idx.get("TLS1_1")),
                "TLS1_2": self._calculate_cell_value(idx.get("TLS1_2")),
                # Inline exceptional handling: any downgraded or missing ⇒ Not supported
                "TLS1_3": ("Enabled" if (isinstance(tls13_val, str) and "offered" in tls13_val.lower() and "not offered" not in tls13_val.lower() and "downgraded" not in tls13_val.lower()) else "Not supported"),
            }

            ciphers = {
                "RC4":        self._calculate_cell_value(idx.get("cipherlist_RC4") or idx.get("RC4")),
                "TRIPLE_DES": self._calculate_cell_value(idx.get("cipherlist_3DES_IDEA")),
                "EXPORT":     self._calculate_cell_value(idx.get("cipherlist_EXPORT")),
                "CBC_OBS":    self._calculate_cell_value(idx.get("cipherlist_OBSOLETED")),
                "PFS":        self._calculate_cell_value(idx.get("FS")),
            }

            endpoints.append({
                "display": display,
                "port": port,
                "protocols": protocols,
                "ciphers": ciphers,
                "_crime":  "vulnerable" in idx.get("CRIME_TLS", "").lower(),
                "_breach": "vulnerable" in idx.get("BREACH", "").lower(),
                "_weakdh": any(w in idx.get("LOGJAM", "").lower() for w in ("weak", "vulnerable")),
            })

        # derive flags (outside the loop, readable)
        def any_protocol_enabled(name: str) -> bool:
            return any(ep["protocols"][name] == "Enabled" for ep in endpoints)

        def any_protocol_missing(name: str) -> bool:
            return any(ep["protocols"][name] != "Enabled" for ep in endpoints)

        def any_cipher_enabled(name: str) -> bool:
            return any(ep["ciphers"][name] == "Enabled" for ep in endpoints)

        flags = {
            "sslv2_enabled": any_protocol_enabled("SSLv2"),
            "sslv3_enabled": any_protocol_enabled("SSLv3"),
            "tls10_enabled": any_protocol_enabled("TLS1_0"),
            "tls11_enabled": any_protocol_enabled("TLS1_1"),
            "tls12_missing": any_protocol_missing("TLS1_2"),
            "tls13_missing": any_protocol_missing("TLS1_3"),
            "pfs_missing":   not any_cipher_enabled("PFS"),
            "rc4_enabled":   any_cipher_enabled("RC4"),
            "tdes_enabled":  any_cipher_enabled("TRIPLE_DES"),
            "export_enabled": any_cipher_enabled("EXPORT"),
            "cbc_obsolete_enabled": any_cipher_enabled("CBC_OBS"),
            "tls_compression_enabled": any(ep["_crime"] for ep in endpoints),
            "http_compression_enabled": any(ep["_breach"] for ep in endpoints),
            "any_weak_dh": any(ep["_weakdh"] for ep in endpoints),
        }
        flags["any_legacy_enabled"] = any(flags[k] for k in ("sslv2_enabled", "sslv3_enabled", "tls10_enabled", "tls11_enabled"))

        for ep in endpoints:
            ep.pop("_crime", None)
            ep.pop("_breach", None)
            ep.pop("_weakdh", None)

        return {"scope": {"flags": flags}, "endpoints": endpoints}

    def process(self, inputs: List[Path], options: Dict[str, Any]) -> Dict[str, Any]:
        """Engine entrypoint.
        inputs: list of Paths chosen by the engine (dir/files/glob)
        options: arbitrary dict from config (currently unused)
        """
        records: List[Dict[str, Any]] = []
        for p in inputs:
            data = json.loads(Path(p).read_text(encoding="utf-8"))
            if isinstance(data, list):
                records.extend(data)
            elif isinstance(data, dict) and isinstance(data.get("results"), list):
                records.extend(data["results"])
            else:
                raise ValueError(f"Unsupported JSON structure in {p}")
        return self._build_context(records)
